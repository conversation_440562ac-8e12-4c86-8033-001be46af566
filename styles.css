/* Import Modern Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

/* CSS Custom Properties - Soft Blue/White Theme */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #93c5fd;
    --secondary-color: #64748b;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-blue-light: #eff6ff;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    --radius-full: 9999px;

    /* Modern Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* Advanced Shadows */
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
    --shadow-glow-lg: 0 0 40px rgba(59, 130, 246, 0.4);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-colored: 0 10px 25px -5px rgba(59, 130, 246, 0.2);

    /* Modern Transitions */
    --transition-fast: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 500ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-spring: all 400ms cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --glass-backdrop: blur(8px);

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1050;
    --z-tooltip: 1070;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: var(--font-sans);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    font-weight: 400;
    font-size: 1rem;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: 'kern' 1;
    font-kerning: normal;
}

/* Modern Typography Scale */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-sans);
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
    margin-bottom: 0.5em;
    letter-spacing: -0.025em;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    letter-spacing: -0.05em;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    letter-spacing: -0.04em;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 600;
    letter-spacing: -0.03em;
}

h4 {
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: -0.02em;
}

h5 {
    font-size: 1.25rem;
    font-weight: 600;
}

h6 {
    font-size: 1.125rem;
    font-weight: 600;
}

p {
    margin-bottom: 1em;
    color: var(--text-secondary);
    line-height: 1.7;
}

.text-large {
    font-size: 1.25rem;
    line-height: 1.6;
}

.text-small {
    font-size: 0.875rem;
}

.font-display {
    font-family: 'Playfair Display', Georgia, serif;
}

.font-mono {
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Selection styling */
::selection {
    background: var(--primary-light);
    color: white;
}

/* Focus styles */
*:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 20px;
}

.logo h1 {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.875rem;
    font-weight: 800;
    letter-spacing: -0.025em;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    align-items: center;
}

.nav a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
    position: relative;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
}

.nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    transition: width 0.3s ease;
}

.nav a:hover {
    color: var(--primary-color);
}

.nav a:hover::after {
    width: 100%;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: background-color 0.3s ease;
}

.mobile-menu-toggle:hover {
    background-color: var(--bg-tertiary);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    margin: 2px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

/* Modern Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: var(--gradient-primary);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.7;
    animation: float 6s ease-in-out infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    background: var(--gradient-accent);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 200px;
    height: 200px;
    background: var(--gradient-warm);
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.orb-3 {
    width: 150px;
    height: 150px;
    background: var(--gradient-cool);
    bottom: 20%;
    left: 60%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

.hero .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content {
    color: white;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 2rem;
    animation: slideInUp 0.8s ease-out;
}

.badge-icon {
    font-size: 1rem;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    animation: slideInUp 0.8s ease-out 0.2s both;
}

.gradient-text {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 2rem;
    animation: slideInUp 0.8s ease-out 0.4s both;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    animation: slideInUp 0.8s ease-out 0.6s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #fbbf24;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    animation: slideInUp 0.8s ease-out 0.8s both;
}

.btn-primary-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: var(--text-primary);
    padding: 1rem 2rem;
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.125rem;
    transition: var(--transition-spring);
    box-shadow: var(--shadow-xl);
    border: none;
    cursor: pointer;
}

.btn-primary-modern:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-glow-lg);
}

.btn-icon {
    width: 20px;
    height: 20px;
    stroke-width: 2;
}

.btn-secondary-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    color: white;
    padding: 1rem 2rem;
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: 1.125rem;
    transition: var(--transition-spring);
    cursor: pointer;
}

.btn-secondary-modern:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.play-icon {
    width: 20px;
    height: 20px;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hero Visual - Phone Mockup */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: slideInRight 0.8s ease-out 1s both;
}

.phone-mockup {
    width: 280px;
    height: 560px;
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-radius: 2.5rem;
    padding: 1rem;
    box-shadow: var(--shadow-2xl);
    position: relative;
    overflow: hidden;
}

.phone-mockup::before {
    content: '';
    position: absolute;
    top: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: #4a5568;
    border-radius: 2px;
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    border-radius: 2rem;
    overflow: hidden;
    position: relative;
}

.app-interface {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.interface-header {
    padding: 1rem;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.header-dots {
    display: flex;
    gap: 0.5rem;
}

.header-dots span {
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
}

.interface-content {
    flex: 1;
    padding: 1.5rem;
    background: var(--bg-secondary);
}

.booking-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    height: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.form-preview {
    space-y: 1rem;
}

.input-line {
    height: 12px;
    background: var(--bg-tertiary);
    border-radius: 6px;
    margin-bottom: 1rem;
    animation: shimmer 2s infinite;
}

.input-line.short {
    width: 60%;
}

.date-picker {
    margin-top: 1.5rem;
}

.calendar-grid {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.calendar-day {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.calendar-day.active {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes shimmer {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Modern Features Section */
.features {
    padding: 120px 0;
    background: var(--bg-primary);
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 5rem;
}

.section-badge {
    display: inline-block;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.section-description {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card.modern {
    background: white;
    border-radius: var(--radius-2xl);
    padding: 2.5rem;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-light);
    transition: var(--transition-spring);
    cursor: pointer;
}

.feature-card.modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: 1;
}

.feature-card.modern:hover::before {
    opacity: 0.05;
}

.feature-card.modern:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-200);
}

.feature-icon-wrapper {
    position: relative;
    z-index: 2;
    margin-bottom: 1.5rem;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: var(--transition-spring);
}

.feature-card.modern:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-icon svg {
    width: 28px;
    height: 28px;
    stroke-width: 2;
}

.feature-content {
    position: relative;
    z-index: 2;
}

.feature-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    transition: var(--transition-normal);
}

.feature-card.modern:hover .feature-content h3 {
    color: var(--primary-600);
}

.feature-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1rem;
}

.feature-hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, var(--primary-100) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition-normal);
    pointer-events: none;
}

.feature-card.modern:hover .feature-hover-effect {
    opacity: 0.3;
}

/* Scroll Animations */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-up.animate {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-left.animate {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-right.animate {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in.animate {
    opacity: 1;
    transform: scale(1);
}

/* Stagger Animation Delays */
.stagger-1 { transition-delay: 0.1s; }
.stagger-2 { transition-delay: 0.2s; }
.stagger-3 { transition-delay: 0.3s; }
.stagger-4 { transition-delay: 0.4s; }
.stagger-5 { transition-delay: 0.5s; }
.stagger-6 { transition-delay: 0.6s; }

/* Interactive Button Enhancements */
.btn-primary-modern,
.btn-secondary-modern {
    position: relative;
    overflow: hidden;
}

.btn-primary-modern::before,
.btn-secondary-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary-modern:hover::before,
.btn-secondary-modern:hover::before {
    left: 100%;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    box-shadow: var(--shadow-xl);
    transition: var(--transition-spring);
    z-index: var(--z-fixed);
    animation: fabPulse 2s infinite;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow-lg);
}

.fab svg {
    width: 24px;
    height: 24px;
}

@keyframes fabPulse {
    0%, 100% { box-shadow: var(--shadow-xl); }
    50% { box-shadow: var(--shadow-glow-lg); }
}

/* Parallax Background */
.parallax-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 120%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="white" stop-opacity="0.05"/><stop offset="100%" stop-color="white" stop-opacity="0"/></radialGradient></defs><circle cx="100" cy="100" r="50" fill="url(%23a)"/><circle cx="900" cy="200" r="80" fill="url(%23a)"/><circle cx="200" cy="800" r="60" fill="url(%23a)"/><circle cx="800" cy="700" r="70" fill="url(%23a)"/></svg>');
    animation: parallaxFloat 20s linear infinite;
    pointer-events: none;
}

@keyframes parallaxFloat {
    0% { transform: translateY(0); }
    100% { transform: translateY(-100px); }
}

/* Services Section */
.services {
    padding: 100px 0;
    background: var(--bg-secondary);
    position: relative;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.services h2 {
    text-align: center;
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-weight: 800;
    letter-spacing: -0.02em;
}

.services .section-subtitle {
    text-align: center;
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.service-card {
    background: white;
    padding: 2.5rem;
    border-radius: var(--radius-xl);
    text-align: left;
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.service-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.service-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.service-card h3 {
    font-size: 1.5rem;
    margin: 0;
    color: var(--text-primary);
    font-weight: 700;
    line-height: 1.3;
}

.service-card p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.7;
    font-size: 0.95rem;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.service-features li::before {
    content: '✓';
    color: var(--accent-color);
    font-weight: 600;
    font-size: 1rem;
}

.service-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.service-price {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
}

.service-price .period {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
    min-width: 140px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-secondary {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary:active {
    transform: translateY(0);
}

/* Button variants */
.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--border-color);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="b" cx="50%" cy="50%"><stop offset="0%" stop-color="white" stop-opacity="0.05"/><stop offset="100%" stop-color="white" stop-opacity="0"/></radialGradient></defs><circle cx="100" cy="100" r="80" fill="url(%23b)"/><circle cx="900" cy="200" r="120" fill="url(%23b)"/><circle cx="300" cy="800" r="100" fill="url(%23b)"/></svg>');
}

.contact .container {
    position: relative;
    z-index: 2;
}

.contact h2 {
    text-align: center;
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 1rem;
    font-weight: 800;
    letter-spacing: -0.02em;
}

.contact .section-subtitle {
    text-align: center;
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.contact-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    padding: 2rem;
    border-radius: var(--radius-xl);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.contact-item .icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.5rem;
}

.contact-item h4 {
    color: white;
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.contact-item p {
    opacity: 0.9;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: white;
    padding: 60px 0 30px;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h4 {
    margin-bottom: 1.5rem;
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-section a:hover {
    color: var(--primary-light);
    transform: translateX(4px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-content {
    background-color: white;
    margin: 3% auto;
    padding: 2.5rem;
    border-radius: var(--radius-xl);
    width: 90%;
    max-width: 550px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--shadow-xl);
    animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.close {
    color: var(--text-light);
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
    font-size: 24px;
    font-weight: 400;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.modal h2 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    text-align: center;
    font-weight: 700;
    font-size: 1.75rem;
}

.modal .modal-subtitle {
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 0.95rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
    background: var(--bg-primary);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
    line-height: 1.6;
}

.form-group select {
    cursor: pointer;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        display: none;
        z-index: 999;
    }

    .nav.active {
        display: block;
    }

    .nav ul {
        flex-direction: column;
        padding: 1rem 0;
        gap: 0;
    }

    .nav ul li {
        padding: 0.5rem 2rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .nav ul li:last-child {
        border-bottom: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        padding: 1rem 20px;
    }

    .hero {
        padding: 120px 0 80px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem;
    }

    .service-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .service-footer {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .contact-info {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .modal-content {
        margin: 5% auto;
        width: 95%;
        padding: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .services, .contact {
        padding: 80px 0;
    }

    .service-card {
        padding: 1.5rem;
    }

    .modal-content {
        margin: 2% auto;
        width: 98%;
        padding: 1.5rem;
        max-height: 95vh;
    }

    .btn-primary, .btn-secondary {
        padding: 1rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Appointment Booking Styles */
.booking-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 120px 20px 80px;
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 3rem;
}

.booking-form {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

.booking-sidebar {
    background: var(--bg-blue-light);
    padding: 2rem;
    border-radius: var(--radius-lg);
    height: fit-content;
    border: 1px solid var(--border-light);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-top: 0.25rem;
}

.sidebar-info {
    margin-bottom: 2rem;
}

.sidebar-info h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

.info-item .icon {
    width: 20px;
    height: 20px;
    color: var(--primary-color);
}

/* Thank You Page Styles */
.thank-you-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 120px 20px 80px;
    text-align: center;
}

.thank-you-card {
    background: var(--bg-primary);
    padding: 3rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
}

.appointment-summary {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--radius-md);
    margin: 2rem 0;
    text-align: left;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.summary-value {
    font-weight: 600;
    color: var(--text-primary);
}

.next-steps {
    text-align: left;
    margin: 2rem 0;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.step-number {
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Admin Styles */
.admin-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 120px 20px 80px;
}

.admin-card {
    background: var(--bg-primary);
    padding: 3rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    text-align: center;
}

.demo-credentials {
    background: var(--bg-blue-light);
    padding: 1.5rem;
    border-radius: var(--radius-md);
    margin: 2rem 0;
    text-align: left;
    border: 1px solid var(--border-light);
}

.demo-credentials h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.credential-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.credential-item:last-child {
    margin-bottom: 0;
}

.error-message {
    background: #fee2e2;
    color: #991b1b;
    padding: 0.75rem;
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
    display: none;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 120px 20px 80px;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.dashboard-title {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
}

.dashboard-actions {
    display: flex;
    gap: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.filters-section {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    margin-bottom: 2rem;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.appointments-table {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.table-responsive {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

tr:hover {
    background: var(--bg-secondary);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--bg-primary);
    margin: 5% auto;
    padding: 2rem;
    border-radius: var(--radius-lg);
    width: 90%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    color: var(--text-secondary);
}

.close:hover {
    color: var(--text-primary);
}

.modal-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-body {
    margin-bottom: 1.5rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-light);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.detail-value {
    font-weight: 600;
    color: var(--text-primary);
}

/* Additional Button Styles */
.btn-danger {
    background-color: #dc2626;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-danger:hover {
    background-color: #b91c1c;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: var(--radius-sm);
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Status badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-confirmed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-completed {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-cancelled {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Modern Mobile Responsiveness */
@media (max-width: 1024px) {
    .hero .container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-visual {
        order: -1;
    }

    .phone-mockup {
        width: 240px;
        height: 480px;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .hero {
        min-height: 90vh;
        padding: 2rem 0;
    }

    .hero .container {
        gap: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .hero-stats .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .btn-primary-modern,
    .btn-secondary-modern {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .phone-mockup {
        width: 200px;
        height: 400px;
        padding: 0.75rem;
    }

    .orb-1 {
        width: 200px;
        height: 200px;
        top: 5%;
        left: -10%;
    }

    .orb-2 {
        width: 150px;
        height: 150px;
        top: 70%;
        right: -10%;
    }

    .orb-3 {
        width: 100px;
        height: 100px;
        bottom: 10%;
        left: 50%;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .feature-card.modern {
        padding: 2rem;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .fab {
        bottom: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
    }

    .fab svg {
        width: 20px;
        height: 20px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .hero-stats {
        display: none;
    }

    .phone-mockup {
        width: 160px;
        height: 320px;
        padding: 0.5rem;
    }

    .feature-card.modern {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
    }

    .feature-icon svg {
        width: 24px;
        height: 24px;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-description {
        font-size: 1rem;
    }
}

/* Touch-Friendly Interactions */
@media (hover: none) and (pointer: coarse) {
    .feature-card.modern:hover {
        transform: none;
    }

    .btn-primary-modern:hover,
    .btn-secondary-modern:hover {
        transform: none;
    }

    .feature-card.modern {
        transition: none;
    }

    /* Larger touch targets */
    .btn-primary-modern,
    .btn-secondary-modern {
        min-height: 48px;
        padding: 1rem 2rem;
    }

    .fab {
        width: 56px;
        height: 56px;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-background {
        background-size: 100% 100%;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .gradient-orb {
        animation: none;
    }

    .parallax-bg {
        animation: none;
    }

    .fab {
        animation: none;
    }
}

/* Mobile Responsive Styles for Appointment System */
@media (max-width: 768px) {
    .booking-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 100px 20px 60px;
    }

    .booking-form,
    .booking-sidebar {
        padding: 1.5rem;
    }

    .thank-you-container {
        padding: 100px 20px 60px;
    }

    .thank-you-card {
        padding: 2rem;
    }

    .summary-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .admin-container {
        padding: 100px 20px 60px;
    }

    .admin-card {
        padding: 2rem;
    }

    .dashboard-container {
        padding: 100px 20px 60px;
    }

    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .dashboard-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    th, td {
        padding: 0.75rem 0.5rem;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .booking-form,
    .booking-sidebar,
    .thank-you-card,
    .admin-card {
        padding: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    .btn-small {
        padding: 2px 6px;
        font-size: 10px;
    }

    th, td {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }

    .modal-content {
        margin: 5% auto;
        padding: 1rem;
    }
}
