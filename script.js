// AppointmentPro - JavaScript Functionality
// Data Storage and Management System

// Local Storage Keys
const STORAGE_KEYS = {
    APPOINTMENTS: 'appointmentpro_appointments',
    ADMIN_SESSION: 'appointmentpro_admin_session',
    ADMIN_CREDENTIALS: 'appointmentpro_admin_credentials'
};

// Admin Credentials (In production, this would be handled server-side)
const ADMIN_CREDENTIALS = {
    username: 'admin',
    password: 'password123'
};

// Initialize admin credentials in localStorage if not exists
if (!localStorage.getItem(STORAGE_KEYS.ADMIN_CREDENTIALS)) {
    localStorage.setItem(STORAGE_KEYS.ADMIN_CREDENTIALS, JSON.stringify(ADMIN_CREDENTIALS));
}

// Utility Functions
function generateBookingId() {
    return 'APT-' + Date.now().toString(36).toUpperCase() + '-' + Math.random().toString(36).substr(2, 5).toUpperCase();
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// Data Management Functions
function saveAppointment(appointmentData) {
    const appointments = getAppointments();
    const newAppointment = {
        ...appointmentData,
        id: generateBookingId(),
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    appointments.push(newAppointment);
    localStorage.setItem(STORAGE_KEYS.APPOINTMENTS, JSON.stringify(appointments));
    return newAppointment;
}

function getAppointments() {
    const stored = localStorage.getItem(STORAGE_KEYS.APPOINTMENTS);
    return stored ? JSON.parse(stored) : [];
}

function updateAppointment(id, updates) {
    const appointments = getAppointments();
    const index = appointments.findIndex(apt => apt.id === id);

    if (index !== -1) {
        appointments[index] = {
            ...appointments[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        localStorage.setItem(STORAGE_KEYS.APPOINTMENTS, JSON.stringify(appointments));
        return appointments[index];
    }
    return null;
}

function deleteAppointment(id) {
    const appointments = getAppointments();
    const filtered = appointments.filter(apt => apt.id !== id);
    localStorage.setItem(STORAGE_KEYS.APPOINTMENTS, JSON.stringify(filtered));
    return filtered;
}

// Admin Authentication Functions
function authenticateAdmin(username, password) {
    const credentials = JSON.parse(localStorage.getItem(STORAGE_KEYS.ADMIN_CREDENTIALS));
    if (credentials.username === username && credentials.password === password) {
        const session = {
            authenticated: true,
            loginTime: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
        };
        localStorage.setItem(STORAGE_KEYS.ADMIN_SESSION, JSON.stringify(session));
        return true;
    }
    return false;
}

function isAdminAuthenticated() {
    const session = localStorage.getItem(STORAGE_KEYS.ADMIN_SESSION);
    if (!session) return false;

    const sessionData = JSON.parse(session);
    const now = new Date();
    const expiresAt = new Date(sessionData.expiresAt);

    return sessionData.authenticated && now < expiresAt;
}

function logoutAdmin() {
    localStorage.removeItem(STORAGE_KEYS.ADMIN_SESSION);
}

// Page-Specific Functionality

// Booking Form Submission
document.addEventListener('DOMContentLoaded', () => {
    const bookingForm = document.getElementById('bookingForm');
    if (bookingForm) {
        bookingForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!validateForm(bookingForm)) {
                alert('Please fill in all required fields correctly.');
                return;
            }

            const formData = new FormData(bookingForm);
            const appointmentData = Object.fromEntries(formData);

            // Disable submit button during processing
            const submitBtn = bookingForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span>Booking...</span>';
            submitBtn.disabled = true;

            try {
                // Save appointment
                const newAppointment = saveAppointment(appointmentData);

                // Store appointment data for thank you page
                sessionStorage.setItem('lastAppointment', JSON.stringify(newAppointment));

                // Redirect to thank you page
                window.location.href = 'thank-you.html';

            } catch (error) {
                console.error('Booking error:', error);
                alert('Failed to book appointment. Please try again.');
            } finally {
                // Re-enable submit button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });
    }
});

// Thank You Page Functionality
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname.includes('thank-you.html')) {
        const lastAppointment = sessionStorage.getItem('lastAppointment');
        if (lastAppointment) {
            const appointment = JSON.parse(lastAppointment);
            populateThankYouPage(appointment);
            sessionStorage.removeItem('lastAppointment');
        }
    }
});

function populateThankYouPage(appointment) {
    document.getElementById('summaryName').textContent = appointment.fullName;
    document.getElementById('summaryEmail').textContent = appointment.email;
    document.getElementById('summaryPhone').textContent = appointment.phone;
    document.getElementById('summaryDate').textContent = formatDate(appointment.appointmentDate);
    document.getElementById('summaryTime').textContent = appointment.appointmentTime;
    document.getElementById('summaryBookingId').textContent = appointment.id;

    if (appointment.serviceType) {
        document.getElementById('summaryService').textContent = appointment.serviceType;
        document.getElementById('serviceRow').style.display = 'flex';
    }
}

// Admin Login Functionality
document.addEventListener('DOMContentLoaded', () => {
    const adminLoginForm = document.getElementById('adminLoginForm');
    if (adminLoginForm) {
        adminLoginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('adminUsername').value;
            const password = document.getElementById('adminPassword').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            const submitBtn = adminLoginForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span>Signing In...</span>';
            submitBtn.disabled = true;

            try {
                if (authenticateAdmin(username, password)) {
                    if (rememberMe) {
                        localStorage.setItem('admin_remember', 'true');
                    }
                    window.location.href = 'admin-dashboard.html';
                } else {
                    document.getElementById('loginError').textContent = 'Invalid username or password';
                    document.getElementById('loginError').style.display = 'block';
                }
            } catch (error) {
                console.error('Login error:', error);
                document.getElementById('loginError').textContent = 'Login failed. Please try again.';
                document.getElementById('loginError').style.display = 'block';
            } finally {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });
    }
});

// Admin Dashboard Functionality
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname.includes('admin-dashboard.html')) {
        // Check authentication
        if (!isAdminAuthenticated()) {
            window.location.href = 'admin-login.html';
            return;
        }

        initializeDashboard();
    }
});

function initializeDashboard() {
    loadAppointments();
    setupDashboardEventListeners();
    updateStatistics();
}

function setupDashboardEventListeners() {
    // Logout button
    document.getElementById('logoutBtn').addEventListener('click', () => {
        logoutAdmin();
        window.location.href = 'admin-login.html';
    });

    // Refresh button
    document.getElementById('refreshBtn').addEventListener('click', () => {
        loadAppointments();
        updateStatistics();
    });

    // Export CSV button
    document.getElementById('exportBtn').addEventListener('click', exportToCSV);

    // Filter controls
    document.getElementById('filterDate').addEventListener('change', filterAppointments);
    document.getElementById('filterService').addEventListener('change', filterAppointments);
    document.getElementById('filterStatus').addEventListener('change', filterAppointments);
    document.getElementById('applyFilters').addEventListener('click', filterAppointments);
    document.getElementById('clearFilters').addEventListener('click', clearFilters);

    // Modal action buttons
    document.getElementById('updateStatusBtn').addEventListener('click', () => {
        const modal = document.getElementById('appointmentModal');
        const appointmentId = modal.dataset.appointmentId;
        if (appointmentId) {
            const newStatus = prompt('Enter new status (pending, confirmed, completed, cancelled):');
            if (newStatus && ['pending', 'confirmed', 'completed', 'cancelled'].includes(newStatus)) {
                updateAppointmentStatus(appointmentId, newStatus);
                modal.style.display = 'none';
            }
        }
    });

    document.getElementById('deleteAppointmentBtn').addEventListener('click', () => {
        const modal = document.getElementById('appointmentModal');
        const appointmentId = modal.dataset.appointmentId;
        if (appointmentId && confirm('Are you sure you want to delete this appointment?')) {
            deleteAppointment(appointmentId);
            loadAppointments();
            updateStatistics();
            modal.style.display = 'none';
        }
    });

    // Modal close functionality
    document.querySelector('.close').addEventListener('click', () => {
        document.getElementById('appointmentModal').style.display = 'none';
    });

    window.addEventListener('click', (event) => {
        const modal = document.getElementById('appointmentModal');
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

function loadAppointments() {
    const appointments = getAppointments();
    const tbody = document.getElementById('appointmentsTableBody');
    tbody.innerHTML = '';

    appointments.forEach(appointment => {
        const row = createAppointmentRow(appointment);
        tbody.appendChild(row);
    });
}

function createAppointmentRow(appointment) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${appointment.id}</td>
        <td>${appointment.fullName}</td>
        <td>${appointment.email}</td>
        <td>${appointment.phone}</td>
        <td>${formatDate(appointment.appointmentDate)}</td>
        <td>${appointment.appointmentTime}</td>
        <td>${appointment.serviceType || 'General'}</td>
        <td><span class="status-badge status-${appointment.status}">${appointment.status}</span></td>
        <td>
            <button class="btn-small btn-primary" onclick="viewAppointment('${appointment.id}')">View</button>
            <button class="btn-small btn-secondary" onclick="updateAppointmentStatus('${appointment.id}', 'confirmed')">Confirm</button>
        </td>
    `;
    return row;
}

function updateStatistics() {
    const appointments = getAppointments();
    const today = new Date().toDateString();
    const thisWeek = new Date();
    thisWeek.setDate(thisWeek.getDate() + 7);
    const last24Hours = new Date();
    last24Hours.setHours(last24Hours.getHours() - 24);

    document.getElementById('totalAppointments').textContent = appointments.length;
    document.getElementById('todayAppointments').textContent =
        appointments.filter(apt => new Date(apt.appointmentDate).toDateString() === today).length;
    document.getElementById('upcomingAppointments').textContent =
        appointments.filter(apt => {
            const aptDate = new Date(apt.appointmentDate);
            return aptDate >= new Date() && aptDate <= thisWeek;
        }).length;
    document.getElementById('recentBookings').textContent =
        appointments.filter(apt => new Date(apt.createdAt) >= last24Hours).length;
}

function viewAppointment(id) {
    const appointments = getAppointments();
    const appointment = appointments.find(apt => apt.id === id);

    if (appointment) {
        // Populate modal with appointment details
        const detailsDiv = document.getElementById('appointmentDetails');
        detailsDiv.innerHTML = `
            <div class="detail-row">
                <span class="detail-label">Booking ID:</span>
                <span class="detail-value">${appointment.id}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Name:</span>
                <span class="detail-value">${appointment.fullName}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Email:</span>
                <span class="detail-value">${appointment.email}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Phone:</span>
                <span class="detail-value">${appointment.phone}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Date:</span>
                <span class="detail-value">${formatDate(appointment.appointmentDate)}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Time:</span>
                <span class="detail-value">${appointment.appointmentTime}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Service:</span>
                <span class="detail-value">${appointment.serviceType || 'General'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Status:</span>
                <span class="detail-value">${appointment.status}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Created:</span>
                <span class="detail-value">${new Date(appointment.createdAt).toLocaleString()}</span>
            </div>
        `;

        // Store current appointment ID for actions
        document.getElementById('appointmentModal').dataset.appointmentId = id;

        // Show modal
        document.getElementById('appointmentModal').style.display = 'block';
    }
}

function updateAppointmentStatus(id, status) {
    updateAppointment(id, { status });
    loadAppointments();
    updateStatistics();
}

function filterAppointments() {
    const dateFilter = document.getElementById('filterDate').value;
    const serviceFilter = document.getElementById('filterService').value;
    const statusFilter = document.getElementById('filterStatus').value;

    let appointments = getAppointments();

    if (dateFilter) {
        appointments = appointments.filter(apt => apt.appointmentDate === dateFilter);
    }

    if (serviceFilter) {
        appointments = appointments.filter(apt => apt.serviceType === serviceFilter);
    }

    if (statusFilter) {
        appointments = appointments.filter(apt => apt.status === statusFilter);
    }

    const tbody = document.getElementById('appointmentsTableBody');
    tbody.innerHTML = '';

    appointments.forEach(appointment => {
        const row = createAppointmentRow(appointment);
        tbody.appendChild(row);
    });
}

function clearFilters() {
    document.getElementById('filterDate').value = '';
    document.getElementById('filterService').value = '';
    document.getElementById('filterStatus').value = '';
    loadAppointments(); // Reload all appointments
}

function updateAppointmentStatus(id, newStatus) {
    updateAppointment(id, { status: newStatus });
    loadAppointments();
    updateStatistics();
}

function exportToCSV() {
    const appointments = getAppointments();
    const headers = ['ID', 'Name', 'Email', 'Phone', 'Date', 'Time', 'Service', 'Status', 'Created'];

    const csvContent = [
        headers.join(','),
        ...appointments.map(apt => [
            apt.id,
            apt.fullName,
            apt.email,
            apt.phone,
            apt.appointmentDate,
            apt.appointmentTime,
            apt.serviceType || 'General',
            apt.status,
            new Date(apt.createdAt).toLocaleString()
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `appointments_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
}

// Mobile Menu Toggle
document.addEventListener('DOMContentLoaded', () => {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const nav = document.querySelector('.nav');

    if (mobileMenuToggle && nav) {
        mobileMenuToggle.addEventListener('click', () => {
            nav.classList.toggle('active');
        });
    }
});

// Smooth Scrolling for Navigation Links
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Set minimum date for date inputs (today)
document.addEventListener('DOMContentLoaded', () => {
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];

    dateInputs.forEach(input => {
        input.setAttribute('min', today);
    });
});

// Form Validation
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }

        // Email validation
        if (field.type === 'email' && field.value && !isValidEmail(field.value)) {
            field.classList.add('error');
            isValid = false;
        }

        // Phone validation
        if (field.type === 'tel' && field.value && !isValidPhone(field.value)) {
            field.classList.add('error');
            isValid = false;
        }
    });

    return isValid;
}

// Modal functionality
document.addEventListener('DOMContentLoaded', () => {
    // Close modal when clicking close button or outside modal
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('close') || e.target.classList.contains('modal')) {
            const modal = e.target.closest('.modal') || e.target;
            if (modal.classList.contains('modal')) {
                modal.style.display = 'none';
            }
        }
    });
});

// Add error styles dynamically
const style = document.createElement('style');
style.textContent = `
    .error {
        border-color: var(--danger-color) !important;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
    }

    .status-confirmed {
        background-color: #d1fae5;
        color: #065f46;
    }

    .status-completed {
        background-color: #dbeafe;
        color: #1e40af;
    }

    .status-cancelled {
        background-color: #fee2e2;
        color: #991b1b;
    }

    .btn-small {
        padding: 4px 8px;
        font-size: 12px;
        margin-right: 4px;
    }
`;
document.head.appendChild(style);

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    console.log('ProServices website loaded successfully!');
    
    // Set current year in footer if needed
    const currentYear = new Date().getFullYear();
    const footerText = document.querySelector('.footer-bottom p');
    if (footerText) {
        footerText.textContent = footerText.textContent.replace('2024', currentYear);
    }
});
