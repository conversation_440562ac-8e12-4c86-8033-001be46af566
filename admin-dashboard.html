<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - AppointmentPro</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header admin-header">
        <div class="container">
            <div class="logo">
                <h1>AppointmentPro</h1>
                <span class="admin-badge">Admin</span>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="admin-dashboard.html" class="active">Dashboard</a></li>
                    <li><a href="index.html">View Site</a></li>
                    <li><a href="#" id="logoutBtn">Logout</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard-section">
        <div class="container">
            <div class="dashboard-header">
                <h1>Appointment Dashboard</h1>
                <p>Manage and view all appointment bookings</p>
            </div>

            <!-- Dashboard Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <h3 id="totalAppointments">0</h3>
                        <p>Total Appointments</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h3 id="todayAppointments">0</h3>
                        <p>Today's Appointments</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏰</div>
                    <div class="stat-content">
                        <h3 id="upcomingAppointments">0</h3>
                        <p>Upcoming This Week</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3 id="recentBookings">0</h3>
                        <p>Recent Bookings (24h)</p>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <h3>Filter Appointments</h3>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="filterDate">Filter by Date</label>
                        <input type="date" id="filterDate" name="filterDate">
                    </div>
                    <div class="filter-group">
                        <label for="filterService">Filter by Service</label>
                        <select id="filterService" name="filterService">
                            <option value="">All Services</option>
                            <option value="Business Consultation">Business Consultation</option>
                            <option value="Technical Support">Technical Support</option>
                            <option value="Financial Planning">Financial Planning</option>
                            <option value="Marketing Strategy">Marketing Strategy</option>
                            <option value="General Consultation">General Consultation</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="filterStatus">Filter by Status</label>
                        <select id="filterStatus" name="filterStatus">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="confirmed">Confirmed</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="filter-actions">
                        <button id="applyFilters" class="btn-primary">Apply Filters</button>
                        <button id="clearFilters" class="btn-secondary">Clear All</button>
                    </div>
                </div>
            </div>

            <!-- Appointments Table -->
            <div class="appointments-section">
                <div class="section-header">
                    <h3>All Appointments</h3>
                    <div class="table-actions">
                        <button id="exportBtn" class="btn-secondary">Export CSV</button>
                        <button id="refreshBtn" class="btn-primary">Refresh</button>
                    </div>
                </div>

                <div class="table-container">
                    <table id="appointmentsTable" class="appointments-table">
                        <thead>
                            <tr>
                                <th>Booking ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Date</th>
                                <th>Time</th>
                                <th>Service</th>
                                <th>Status</th>
                                <th>Booked On</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="appointmentsTableBody">
                            <!-- Appointments will be populated here -->
                        </tbody>
                    </table>
                </div>

                <div id="noAppointments" class="no-appointments" style="display: none;">
                    <div class="no-appointments-icon">📅</div>
                    <h3>No Appointments Found</h3>
                    <p>There are no appointments matching your current filters.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Appointment Details Modal -->
    <div id="appointmentModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Appointment Details</h2>
            <div id="appointmentDetails" class="appointment-details">
                <!-- Details will be populated here -->
            </div>
            <div class="modal-actions">
                <button id="updateStatusBtn" class="btn-primary">Update Status</button>
                <button id="deleteAppointmentBtn" class="btn-danger">Delete Appointment</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>AppointmentPro Admin</h4>
                    <p>Administrative dashboard for appointment management.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Actions</h4>
                    <ul>
                        <li><a href="admin-dashboard.html">Dashboard</a></li>
                        <li><a href="index.html">View Site</a></li>
                        <li><a href="#" id="logoutFooter">Logout</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 AppointmentPro. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
