<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - AppointmentPro</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1>AppointmentPro</h1>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="booking.html">Book Now</a></li>
                    <li><a href="terms.html">Terms & Conditions</a></li>
                    <li><a href="admin-login.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <!-- Admin Login Section -->
    <section class="admin-login-section">
        <div class="container">
            <div class="login-container">
                <div class="login-header">
                    <div class="admin-icon">🔐</div>
                    <h1>Admin Login</h1>
                    <p>Access the appointment management dashboard</p>
                </div>

                <form id="adminLoginForm" class="login-form">
                    <div class="form-group">
                        <label for="adminUsername">Username</label>
                        <input type="text" id="adminUsername" name="adminUsername" required placeholder="Enter your username">
                    </div>

                    <div class="form-group">
                        <label for="adminPassword">Password</label>
                        <input type="password" id="adminPassword" name="adminPassword" required placeholder="Enter your password">
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="rememberMe" name="rememberMe">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                    </div>

                    <button type="submit" class="btn-primary login-submit">
                        <span>Login</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M21 12H9"/>
                        </svg>
                    </button>

                    <div id="loginError" class="error-message" style="display: none;">
                        Invalid username or password. Please try again.
                    </div>
                </form>

                <div class="login-info">
                    <h3>Demo Credentials</h3>
                    <p>For demonstration purposes, use:</p>
                    <div class="demo-credentials">
                        <div class="credential-item">
                            <strong>Username:</strong> admin
                        </div>
                        <div class="credential-item">
                            <strong>Password:</strong> password123
                        </div>
                    </div>
                    <p class="demo-note">
                        <em>Note: This is a demo application. In a production environment, proper authentication and security measures would be implemented.</em>
                    </p>
                </div>

                <div class="security-features">
                    <h3>Security Features</h3>
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon">🔒</div>
                            <span>Secure login authentication</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">👥</div>
                            <span>Role-based access control</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">📊</div>
                            <span>Appointment management dashboard</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🔍</div>
                            <span>Advanced filtering and search</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>AppointmentPro</h4>
                    <p>Making appointment scheduling simple and efficient.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="booking.html">Book Appointment</a></li>
                        <li><a href="terms.html">Terms & Conditions</a></li>
                        <li><a href="admin-login.html">Admin Login</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 AppointmentPro. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
